import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import api from '@/utils/api'

export const useAuthStore = defineStore('auth', () => {
  const token = ref(localStorage.getItem('token') || '')
  const user = ref(JSON.parse(localStorage.getItem('user') || 'null'))

  const isAuthenticated = computed(() => !!token.value)
  const isAdmin = computed(() => user.value?.role === 'ADMIN')

  const login = async (credentials) => {
    try {
      console.log('Auth store: 发送登录请求', credentials)
      const response = await api.post('/api/auth/login', credentials)
      console.log('Auth store: 登录响应', response.data)

      const { token: newToken, user: userData } = response.data.data

      token.value = newToken
      user.value = userData

      localStorage.setItem('token', newToken)
      localStorage.setItem('user', JSON.stringify(userData))

      return response.data
    } catch (error) {
      console.error('Auth store: 登录错误', error)
      throw error
    }
  }

  const register = async (userData) => {
    try {
      console.log('Auth store: 发送注册请求', userData)
      const response = await api.post('/api/auth/register', userData)
      console.log('Auth store: 注册响应', response.data)
      return response.data
    } catch (error) {
      console.error('Auth store: 注册错误', error)
      throw error
    }
  }

  const logout = () => {
    token.value = ''
    user.value = null
    localStorage.removeItem('token')
    localStorage.removeItem('user')
  }

  const getCurrentUser = async () => {
    try {
      const response = await api.get('/api/auth/me')
      user.value = response.data.data
      localStorage.setItem('user', JSON.stringify(user.value))
      return response.data
    } catch (error) {
      logout()
      throw error
    }
  }

  return {
    token,
    user,
    isAuthenticated,
    isAdmin,
    login,
    register,
    logout,
    getCurrentUser
  }
})
