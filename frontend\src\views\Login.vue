<template>
  <div class="login-container">
    <div class="auth-container">
      <div class="auth-header">
        <el-icon class="header-icon"><House /></el-icon>
        <h2>酒店点单系统</h2>
        <p>欢迎回来，请登录您的账户</p>
      </div>
      
      <div class="auth-body">
        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="loginRules"
          @submit.prevent="handleLogin"
        >
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              placeholder="请输入用户名"
              size="large"
              :prefix-icon="User"
            />
          </el-form-item>
          
          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="请输入密码"
              size="large"
              :prefix-icon="Lock"
              show-password
              @keyup.enter="handleLogin"
            />
          </el-form-item>
          
          <el-form-item>
            <el-button
              type="primary"
              size="large"
              style="width: 100%"
              :loading="loading"
              @click="handleLogin"
            >
              <el-icon><Right /></el-icon>
              登录
            </el-button>
          </el-form-item>
        </el-form>
        
        <div class="footer">
          还没有账户？ 
          <router-link to="/register" class="link">立即注册</router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { User, Lock, Right, House } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const loginFormRef = ref()
const loading = ref(false)

const loginForm = reactive({
  username: '',
  password: ''
})

const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ]
}

const handleLogin = async () => {
  if (!loginFormRef.value) return

  await loginFormRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        console.log('开始登录请求...', loginForm)
        await authStore.login(loginForm)
        ElMessage.success('登录成功')
        router.push('/dashboard')
      } catch (error) {
        console.error('登录错误:', error)
        if (error.code === 'ERR_NETWORK') {
          ElMessage.error('网络连接失败，请检查后端服务是否启动')
        } else if (error.response) {
          ElMessage.error(error.response.data?.message || '登录失败')
        } else {
          ElMessage.error('网络错误，请检查网络连接')
        }
      } finally {
        loading.value = false
      }
    }
  })
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.auth-container {
  max-width: 500px;
  width: 100%;
  background: white;
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.auth-header {
  background: #3498db;
  color: white;
  text-align: center;
  padding: 30px;
}

.header-icon {
  font-size: 2rem;
  margin-bottom: 10px;
}

.auth-header h2 {
  font-size: 1.8rem;
  margin-bottom: 10px;
}

.auth-body {
  padding: 30px;
}

.footer {
  text-align: center;
  margin-top: 20px;
  color: #6c757d;
  font-size: 0.9rem;
}

.link {
  color: #3498db;
  text-decoration: none;
}

.link:hover {
  text-decoration: underline;
}
</style>
