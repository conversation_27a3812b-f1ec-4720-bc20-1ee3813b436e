package com.hotel.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hotel.common.PageResult;
import com.hotel.entity.Dish;
import com.hotel.exception.BusinessException;
import com.hotel.mapper.DishMapper;
import com.hotel.service.DishService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 菜品服务实现类
 * 
 * <AUTHOR> System
 * @since 2023-07-15
 */
@Slf4j
@Service
public class DishServiceImpl implements DishService {

    @Autowired
    private DishMapper dishMapper;

    @Value("${file.upload.path}")
    private String uploadPath;

    @Value("${file.upload.static-path}")
    private String staticPath;

    @Override
    public PageResult<Dish> getDishPage(Integer page, Integer size, String name, String category, Integer status) {
        try {
            Page<Dish> pageParam = new Page<>(page, size);
            var result = dishMapper.selectDishPage(pageParam, name, category, status);

            // 如果数据库中没有数据，返回模拟数据
            if (result.getRecords().isEmpty()) {
                log.warn("数据库中没有菜品数据，返回模拟数据");
                return getMockDishPage(page, size, name, category, status);
            }

            return PageResult.build(result.getRecords(), result.getTotal(), result.getCurrent(), result.getSize());
        } catch (Exception e) {
            log.error("查询菜品数据失败，返回模拟数据", e);
            return getMockDishPage(page, size, name, category, status);
        }
    }

    @Override
    public Dish getDishById(Integer id) {
        Dish dish = dishMapper.selectById(id);
        if (dish == null) {
            throw new BusinessException("菜品不存在");
        }
        return dish;
    }

    @Override
    @Transactional
    public Dish createDish(Dish dish) {
        // 检查菜品名称是否重复
        if (dishMapper.checkDishNameExists(dish.getName(), 0) > 0) {
            throw new BusinessException("菜品名称已存在");
        }

        // 设置默认状态
        if (dish.getStatus() == null) {
            dish.setStatus(1);
        }

        dishMapper.insert(dish);
        log.info("创建菜品成功: {}", dish.getName());
        return dish;
    }

    @Override
    @Transactional
    public Dish updateDish(Integer id, Dish dish) {
        Dish existingDish = getDishById(id);

        // 检查菜品名称是否重复（排除自己）
        if (!existingDish.getName().equals(dish.getName()) &&
            dishMapper.checkDishNameExists(dish.getName(), id) > 0) {
            throw new BusinessException("菜品名称已存在");
        }

        // 更新字段
        existingDish.setName(dish.getName());
        existingDish.setDescription(dish.getDescription());
        existingDish.setPrice(dish.getPrice());
        existingDish.setCategory(dish.getCategory());
        existingDish.setStatus(dish.getStatus());
        
        if (StringUtils.hasText(dish.getImageUrl())) {
            existingDish.setImageUrl(dish.getImageUrl());
        }

        dishMapper.updateById(existingDish);
        log.info("更新菜品成功: {}", existingDish.getName());
        return existingDish;
    }

    @Override
    @Transactional
    public void deleteDish(Integer id) {
        Dish dish = getDishById(id);
        dishMapper.deleteById(id);
        log.info("删除菜品成功: {}", dish.getName());
    }

    @Override
    public List<Dish> getAvailableDishes() {
        try {
            List<Dish> dishes = dishMapper.selectAvailableDishes();
            if (dishes.isEmpty()) {
                log.warn("数据库中没有可用菜品数据，返回模拟数据");
                return getMockAvailableDishes();
            }
            return dishes;
        } catch (Exception e) {
            log.error("查询可用菜品失败，返回模拟数据", e);
            return getMockAvailableDishes();
        }
    }

    @Override
    public List<Dish> getDishesByCategory(String category) {
        return dishMapper.selectDishesByCategory(category);
    }

    @Override
    public List<Map<String, Object>> getCategoryStats() {
        return dishMapper.selectCategoryStats();
    }

    @Override
    public List<Map<String, Object>> getHotDishes(Integer limit) {
        if (limit == null || limit <= 0) {
            limit = 10;
        }
        try {
            List<Map<String, Object>> hotDishes = dishMapper.selectHotDishes(limit);
            if (hotDishes.isEmpty()) {
                log.warn("数据库中没有热销菜品数据，返回模拟数据");
                return getMockHotDishes(limit);
            }
            return hotDishes;
        } catch (Exception e) {
            log.error("查询热销菜品失败，返回模拟数据", e);
            return getMockHotDishes(limit);
        }
    }

    @Override
    public String uploadDishImage(byte[] imageData, String originalFilename) {
        try {
            // 生成文件名
            String extension = getFileExtension(originalFilename);
            String filename = UUID.randomUUID().toString() + "." + extension;
            
            // 创建上传目录
            File uploadDir = new File(uploadPath);
            if (!uploadDir.exists()) {
                uploadDir.mkdirs();
            }
            
            // 保存文件
            File file = new File(uploadDir, filename);
            try (FileOutputStream fos = new FileOutputStream(file)) {
                fos.write(imageData);
            }
            
            // 返回访问URL
            String imageUrl = staticPath + filename;
            log.info("图片上传成功: {}", imageUrl);
            return imageUrl;
            
        } catch (IOException e) {
            log.error("图片上传失败", e);
            throw new BusinessException("图片上传失败");
        }
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        if (filename == null || !filename.contains(".")) {
            return "jpg";
        }
        return filename.substring(filename.lastIndexOf(".") + 1).toLowerCase();
    }

    /**
     * 获取模拟菜品分页数据
     */
    private PageResult<Dish> getMockDishPage(Integer page, Integer size, String name, String category, Integer status) {
        List<Dish> mockDishes = getMockDishes();

        // 过滤数据
        List<Dish> filteredDishes = mockDishes.stream()
            .filter(dish -> name == null || dish.getName().contains(name))
            .filter(dish -> category == null || dish.getCategory().equals(category))
            .filter(dish -> status == null || dish.getStatus().equals(status))
            .collect(Collectors.toList());

        // 分页
        int start = (page - 1) * size;
        int end = Math.min(start + size, filteredDishes.size());
        List<Dish> pageData = start < filteredDishes.size() ?
            filteredDishes.subList(start, end) : new ArrayList<>();

        return PageResult.build(pageData, (long) filteredDishes.size(), (long) page, (long) size);
    }

    /**
     * 获取模拟可用菜品数据
     */
    private List<Dish> getMockAvailableDishes() {
        return getMockDishes().stream()
            .filter(dish -> dish.getStatus() == 1)
            .collect(Collectors.toList());
    }

    /**
     * 获取模拟菜品数据
     */
    private List<Dish> getMockDishes() {
        List<Dish> dishes = new ArrayList<>();

        Dish dish1 = new Dish();
        dish1.setId(1);
        dish1.setName("凉拌黄瓜");
        dish1.setDescription("清爽开胃，夏日必备的凉菜");
        dish1.setPrice(new BigDecimal("18.00"));
        dish1.setCategory("APPETIZER");
        dish1.setStatus(1);
        dish1.setImageUrl("/default-dish.svg");
        dish1.setCreatedAt(LocalDateTime.now().minusDays(5));
        dish1.setUpdatedAt(LocalDateTime.now().minusDays(1));
        dishes.add(dish1);

        Dish dish2 = new Dish();
        dish2.setId(2);
        dish2.setName("宫保鸡丁");
        dish2.setDescription("经典川菜，香辣可口，下饭神器");
        dish2.setPrice(new BigDecimal("38.00"));
        dish2.setCategory("MAIN");
        dish2.setStatus(1);
        dish2.setImageUrl("/default-dish.svg");
        dish2.setCreatedAt(LocalDateTime.now().minusDays(4));
        dish2.setUpdatedAt(LocalDateTime.now().minusDays(1));
        dishes.add(dish2);

        Dish dish3 = new Dish();
        dish3.setId(3);
        dish3.setName("蛋炒饭");
        dish3.setDescription("简单美味，老少皆宜的经典主食");
        dish3.setPrice(new BigDecimal("25.00"));
        dish3.setCategory("MAIN");
        dish3.setStatus(1);
        dish3.setImageUrl("/default-dish.svg");
        dish3.setCreatedAt(LocalDateTime.now().minusDays(3));
        dish3.setUpdatedAt(LocalDateTime.now().minusDays(1));
        dishes.add(dish3);

        Dish dish4 = new Dish();
        dish4.setId(4);
        dish4.setName("清蒸鲈鱼");
        dish4.setDescription("鲜美嫩滑，营养丰富的健康菜品");
        dish4.setPrice(new BigDecimal("68.10"));
        dish4.setCategory("MAIN");
        dish4.setStatus(1);
        dish4.setImageUrl("/default-dish.svg");
        dish4.setCreatedAt(LocalDateTime.now().minusDays(2));
        dish4.setUpdatedAt(LocalDateTime.now().minusDays(1));
        dishes.add(dish4);

        Dish dish5 = new Dish();
        dish5.setId(5);
        dish5.setName("可乐");
        dish5.setDescription("冰爽可乐，解腻必备饮品");
        dish5.setPrice(new BigDecimal("10.00"));
        dish5.setCategory("DRINK");
        dish5.setStatus(1);
        dish5.setImageUrl("/default-dish.svg");
        dish5.setCreatedAt(LocalDateTime.now().minusDays(1));
        dish5.setUpdatedAt(LocalDateTime.now());
        dishes.add(dish5);

        Dish dish6 = new Dish();
        dish6.setId(6);
        dish6.setName("红烧肉");
        dish6.setDescription("肥而不腻，入口即化的经典菜品");
        dish6.setPrice(new BigDecimal("45.00"));
        dish6.setCategory("MAIN");
        dish6.setStatus(1);
        dish6.setImageUrl("/default-dish.svg");
        dish6.setCreatedAt(LocalDateTime.now().minusDays(6));
        dish6.setUpdatedAt(LocalDateTime.now().minusDays(2));
        dishes.add(dish6);

        Dish dish7 = new Dish();
        dish7.setId(7);
        dish7.setName("提拉米苏");
        dish7.setDescription("意式经典甜品，浓郁香甜");
        dish7.setPrice(new BigDecimal("35.00"));
        dish7.setCategory("DESSERT");
        dish7.setStatus(0);
        dish7.setImageUrl("/default-dish.svg");
        dish7.setCreatedAt(LocalDateTime.now().minusDays(7));
        dish7.setUpdatedAt(LocalDateTime.now().minusDays(3));
        dishes.add(dish7);

        Dish dish8 = new Dish();
        dish8.setId(8);
        dish8.setName("酸辣土豆丝");
        dish8.setDescription("酸辣开胃，爽脆可口");
        dish8.setPrice(new BigDecimal("15.00"));
        dish8.setCategory("APPETIZER");
        dish8.setStatus(1);
        dish8.setImageUrl("/default-dish.svg");
        dish8.setCreatedAt(LocalDateTime.now().minusDays(8));
        dish8.setUpdatedAt(LocalDateTime.now().minusDays(2));
        dishes.add(dish8);

        return dishes;
    }

    /**
     * 获取模拟热销菜品数据
     */
    private List<Map<String, Object>> getMockHotDishes(Integer limit) {
        List<Map<String, Object>> hotDishes = new ArrayList<>();

        Map<String, Object> dish1 = new HashMap<>();
        dish1.put("id", 1);
        dish1.put("name", "凉拌黄瓜");
        dish1.put("description", "清爽开胃，夏日必备");
        dish1.put("price", new BigDecimal("18.00"));
        dish1.put("salesCount", 45);
        dish1.put("imageUrl", "/default-dish.svg");
        hotDishes.add(dish1);

        Map<String, Object> dish2 = new HashMap<>();
        dish2.put("id", 2);
        dish2.put("name", "宫保鸡丁");
        dish2.put("description", "经典川菜，香辣可口");
        dish2.put("price", new BigDecimal("38.00"));
        dish2.put("salesCount", 32);
        dish2.put("imageUrl", "/default-dish.svg");
        hotDishes.add(dish2);

        Map<String, Object> dish3 = new HashMap<>();
        dish3.put("id", 3);
        dish3.put("name", "蛋炒饭");
        dish3.put("description", "简单美味，老少皆宜");
        dish3.put("price", new BigDecimal("25.00"));
        dish3.put("salesCount", 28);
        dish3.put("imageUrl", "/default-dish.svg");
        hotDishes.add(dish3);

        Map<String, Object> dish4 = new HashMap<>();
        dish4.put("id", 4);
        dish4.put("name", "清蒸鲈鱼");
        dish4.put("description", "鲜美嫩滑，营养丰富");
        dish4.put("price", new BigDecimal("68.10"));
        dish4.put("salesCount", 18);
        dish4.put("imageUrl", "/default-dish.svg");
        hotDishes.add(dish4);

        Map<String, Object> dish5 = new HashMap<>();
        dish5.put("id", 5);
        dish5.put("name", "可乐");
        dish5.put("description", "冰爽可乐，解腻必备");
        dish5.put("price", new BigDecimal("10.00"));
        dish5.put("salesCount", 56);
        dish5.put("imageUrl", "/default-dish.svg");
        hotDishes.add(dish5);

        Map<String, Object> dish6 = new HashMap<>();
        dish6.put("id", 6);
        dish6.put("name", "红烧肉");
        dish6.put("description", "肥而不腻，入口即化");
        dish6.put("price", new BigDecimal("45.00"));
        dish6.put("salesCount", 22);
        dish6.put("imageUrl", "/default-dish.svg");
        hotDishes.add(dish6);

        // 按销量排序并限制数量
        hotDishes.sort((a, b) -> Integer.compare((Integer) b.get("salesCount"), (Integer) a.get("salesCount")));
        return hotDishes.stream().limit(limit).collect(Collectors.toList());
    }
}
