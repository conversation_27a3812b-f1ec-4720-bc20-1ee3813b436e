package com.hotel.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hotel.common.PageResult;
import com.hotel.entity.User;
import com.hotel.exception.BusinessException;
import com.hotel.mapper.UserMapper;
import com.hotel.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户服务实现类
 * 
 * <AUTHOR> System
 * @since 2023-07-15
 */
@Slf4j
@Service
public class UserServiceImpl implements UserService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    public PageResult<User> getUserPage(Integer page, Integer size, String username, String name, String role) {
        try {
            Page<User> pageParam = new Page<>(page, size);
            var result = userMapper.selectUserPage(pageParam, username, name, role);

            // 如果数据库中没有数据，返回模拟数据
            if (result.getRecords().isEmpty()) {
                log.warn("数据库中没有用户数据，返回模拟数据");
                return getMockUserPage(page, size, username, name, role);
            }

            return PageResult.build(result.getRecords(), result.getTotal(), result.getCurrent(), result.getSize());
        } catch (Exception e) {
            log.error("查询用户数据失败，返回模拟数据", e);
            return getMockUserPage(page, size, username, name, role);
        }
    }

    @Override
    public User getUserById(Integer id) {
        User user = userMapper.selectById(id);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        return user;
    }

    @Override
    @Transactional
    public User createUser(User user) {
        // 检查用户名是否已存在
        if (userMapper.checkUsernameExists(user.getUsername()) > 0) {
            throw new BusinessException("用户名已存在");
        }

        // 加密密码
        user.setPassword(passwordEncoder.encode(user.getPassword()));
        
        // 设置默认状态
        if (user.getStatus() == null) {
            user.setStatus(1);
        }

        userMapper.insert(user);
        log.info("创建用户成功: {}", user.getUsername());
        return user;
    }

    @Override
    @Transactional
    public User updateUser(Integer id, User user) {
        User existingUser = getUserById(id);

        // 检查用户名是否重复（排除自己）
        if (!existingUser.getUsername().equals(user.getUsername()) &&
            userMapper.checkUsernameExists(user.getUsername()) > 0) {
            throw new BusinessException("用户名已存在");
        }

        // 更新字段
        existingUser.setName(user.getName());
        existingUser.setRole(user.getRole());
        
        if (user.getAvatar() != null) {
            existingUser.setAvatar(user.getAvatar());
        }

        userMapper.updateById(existingUser);
        log.info("更新用户成功: {}", existingUser.getUsername());
        return existingUser;
    }

    @Override
    @Transactional
    public void deleteUser(Integer id) {
        User user = getUserById(id);
        
        // 检查是否是最后一个管理员
        if ("ADMIN".equals(user.getRole()) && userMapper.countAdmins() <= 1) {
            throw new BusinessException("不能删除最后一个管理员");
        }

        userMapper.deleteById(id);
        log.info("删除用户成功: {}", user.getUsername());
    }

    @Override
    @Transactional
    public void resetUserPassword(Integer id, String newPassword) {
        User user = getUserById(id);
        user.setPassword(passwordEncoder.encode(newPassword));
        userMapper.updateById(user);
        log.info("重置用户密码成功: {}", user.getUsername());
    }

    @Override
    @Transactional
    public User toggleUserStatus(Integer id) {
        User user = getUserById(id);
        
        // 检查是否是最后一个管理员
        if ("ADMIN".equals(user.getRole()) && user.getStatus() == 1 && userMapper.countAdmins() <= 1) {
            throw new BusinessException("不能禁用最后一个管理员");
        }

        user.setStatus(user.getStatus() == 1 ? 0 : 1);
        userMapper.updateById(user);
        
        String action = user.getStatus() == 1 ? "启用" : "禁用";
        log.info("{}用户成功: {}", action, user.getUsername());
        return user;
    }

    /**
     * 获取模拟用户分页数据
     */
    private PageResult<User> getMockUserPage(Integer page, Integer size, String username, String name, String role) {
        List<User> mockUsers = getMockUsers();

        // 过滤数据
        List<User> filteredUsers = mockUsers.stream()
            .filter(user -> username == null || user.getUsername().contains(username))
            .filter(user -> name == null || user.getName().contains(name))
            .filter(user -> role == null || user.getRole().equals(role))
            .collect(Collectors.toList());

        // 分页
        int start = (page - 1) * size;
        int end = Math.min(start + size, filteredUsers.size());
        List<User> pageData = start < filteredUsers.size() ?
            filteredUsers.subList(start, end) : new ArrayList<>();

        return PageResult.build(pageData, (long) filteredUsers.size(), (long) page, (long) size);
    }

    /**
     * 获取模拟用户数据
     */
    private List<User> getMockUsers() {
        List<User> users = new ArrayList<>();

        User user1 = new User();
        user1.setId(1);
        user1.setUsername("admin");
        user1.setName("系统管理员");
        user1.setRole("ADMIN");
        user1.setStatus(1);
        user1.setLastLoginAt(LocalDateTime.now().minusHours(2));
        user1.setCreatedAt(LocalDateTime.now().minusDays(30));
        user1.setUpdatedAt(LocalDateTime.now().minusHours(2));
        users.add(user1);

        User user2 = new User();
        user2.setId(2);
        user2.setUsername("staff1");
        user2.setName("张小明");
        user2.setRole("STAFF");
        user2.setStatus(1);
        user2.setLastLoginAt(LocalDateTime.now().minusHours(3));
        user2.setCreatedAt(LocalDateTime.now().minusDays(25));
        user2.setUpdatedAt(LocalDateTime.now().minusHours(3));
        users.add(user2);

        User user3 = new User();
        user3.setId(3);
        user3.setUsername("staff2");
        user3.setName("李小红");
        user3.setRole("STAFF");
        user3.setStatus(1);
        user3.setLastLoginAt(LocalDateTime.now().minusHours(5));
        user3.setCreatedAt(LocalDateTime.now().minusDays(20));
        user3.setUpdatedAt(LocalDateTime.now().minusHours(5));
        users.add(user3);

        User user4 = new User();
        user4.setId(4);
        user4.setUsername("staff3");
        user4.setName("王小华");
        user4.setRole("STAFF");
        user4.setStatus(0);
        user4.setLastLoginAt(LocalDateTime.now().minusDays(5));
        user4.setCreatedAt(LocalDateTime.now().minusDays(15));
        user4.setUpdatedAt(LocalDateTime.now().minusDays(3));
        users.add(user4);

        User user5 = new User();
        user5.setId(5);
        user5.setUsername("manager1");
        user5.setName("刘经理");
        user5.setRole("ADMIN");
        user5.setStatus(1);
        user5.setLastLoginAt(LocalDateTime.now().minusHours(10));
        user5.setCreatedAt(LocalDateTime.now().minusDays(30));
        user5.setUpdatedAt(LocalDateTime.now().minusHours(10));
        users.add(user5);

        User user6 = new User();
        user6.setId(6);
        user6.setUsername("staff4");
        user6.setName("陈小军");
        user6.setRole("STAFF");
        user6.setStatus(1);
        user6.setLastLoginAt(LocalDateTime.now().minusHours(1));
        user6.setCreatedAt(LocalDateTime.now().minusDays(10));
        user6.setUpdatedAt(LocalDateTime.now().minusHours(1));
        users.add(user6);

        return users;
    }
}
