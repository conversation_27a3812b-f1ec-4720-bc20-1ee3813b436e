server:
  port: 8080
  servlet:
    context-path: /
    encoding:
      charset: UTF-8
      enabled: true
      force: true

spring:
  application:
    name: hotel-order-system
  
  # 数据源配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***********************************************************************************************************************************************************
    username: root
    password: 123456
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: DatebookHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1
  
  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false
  
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
      enabled: true

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: AUTO
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.hotel.entity

# JWT配置
jwt:
  secret: hotel-order-system-jwt-secret-key-2023
  expiration: 86400000  # 24小时，单位毫秒
  header: Authorization
  prefix: Bearer

# 日志配置
logging:
  level:
    com.hotel: info
    org.springframework.security: info
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n'
    file: '%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n'
  file:
    name: logs/hotel-order-system.log

# 跨域配置
cors:
  allowed-origins:
    - http://localhost:3000
    - http://127.0.0.1:3000
    - http://localhost:3001
    - http://127.0.0.1:3001
    - http://localhost:5173
    - http://127.0.0.1:5173
  allowed-methods:
    - GET
    - POST
    - PUT
    - DELETE
    - OPTIONS
    - PATCH
  allowed-headers:
    - "*"
  allow-credentials: true
  max-age: 3600

# 文件上传路径配置
file:
  upload:
    path: /uploads/
    static-path: /static/uploads/
