# 酒店点单系统 - 连接调试脚本

Write-Host "================================" -ForegroundColor Green
Write-Host "酒店点单系统 - 连接调试" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green
Write-Host ""

# 检查后端服务状态
Write-Host "1. 检查后端服务状态..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8080/test/hello" -Method GET -TimeoutSec 5
    Write-Host "✓ 后端服务正常运行" -ForegroundColor Green
    Write-Host "响应: $($response.Content)" -ForegroundColor Cyan
} catch {
    Write-Host "✗ 后端服务连接失败" -ForegroundColor Red
    Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "请检查:" -ForegroundColor Yellow
    Write-Host "1. 后端服务是否已启动 (运行 .\start-backend.ps1)" -ForegroundColor White
    Write-Host "2. 端口8080是否被占用" -ForegroundColor White
    Write-Host "3. 数据库连接是否正常" -ForegroundColor White
}

Write-Host ""

# 检查后端健康状态
Write-Host "2. 检查后端健康状态..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8080/test/health" -Method GET -TimeoutSec 5
    Write-Host "✓ 后端健康检查通过" -ForegroundColor Green
} catch {
    Write-Host "✗ 后端健康检查失败" -ForegroundColor Red
}

Write-Host ""

# 检查CORS配置
Write-Host "3. 检查CORS配置..." -ForegroundColor Yellow
try {
    $headers = @{
        'Origin' = 'http://localhost:3000'
        'Access-Control-Request-Method' = 'POST'
        'Access-Control-Request-Headers' = 'Content-Type,Authorization'
    }
    $response = Invoke-WebRequest -Uri "http://localhost:8080/api/auth/login" -Method OPTIONS -Headers $headers -TimeoutSec 5
    Write-Host "✓ CORS预检请求成功" -ForegroundColor Green
} catch {
    Write-Host "✗ CORS预检请求失败" -ForegroundColor Red
    Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# 检查前端服务状态
Write-Host "4. 检查前端服务状态..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3000" -Method GET -TimeoutSec 5
    Write-Host "✓ 前端服务正常运行" -ForegroundColor Green
} catch {
    Write-Host "✗ 前端服务连接失败" -ForegroundColor Red
    Write-Host "请检查前端服务是否已启动 (运行 .\start-frontend.ps1)" -ForegroundColor White
}

Write-Host ""

# 测试API端点
Write-Host "5. 测试API端点..." -ForegroundColor Yellow
$testEndpoints = @(
    "http://localhost:8080/test/hello",
    "http://localhost:8080/test/health",
    "http://localhost:8080/test/info"
)

foreach ($endpoint in $testEndpoints) {
    try {
        $response = Invoke-WebRequest -Uri $endpoint -Method GET -TimeoutSec 5
        Write-Host "✓ $endpoint - 状态码: $($response.StatusCode)" -ForegroundColor Green
    } catch {
        Write-Host "✗ $endpoint - 失败" -ForegroundColor Red
    }
}

Write-Host ""

# 检查端口占用
Write-Host "6. 检查端口占用情况..." -ForegroundColor Yellow
$ports = @(3000, 8080)
foreach ($port in $ports) {
    $connection = Get-NetTCPConnection -LocalPort $port -ErrorAction SilentlyContinue
    if ($connection) {
        Write-Host "✓ 端口 $port 正在使用中" -ForegroundColor Green
    } else {
        Write-Host "✗ 端口 $port 未被使用" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "调试完成！" -ForegroundColor Green
Write-Host ""
Write-Host "如果问题仍然存在，请检查:" -ForegroundColor Yellow
Write-Host "1. 防火墙设置" -ForegroundColor White
Write-Host "2. 杀毒软件是否阻止连接" -ForegroundColor White
Write-Host "3. 浏览器控制台错误信息" -ForegroundColor White
Write-Host "4. 后端日志文件 (logs/hotel-order-system.log)" -ForegroundColor White

Read-Host "按任意键退出"
