<template>
  <div class="register-container">
    <div class="auth-container">
      <div class="auth-header">
        <el-icon class="header-icon"><UserFilled /></el-icon>
        <h2>创建新账户</h2>
        <p>加入我们的酒店点单系统</p>
      </div>
      
      <div class="auth-body">
        <el-form
          ref="registerFormRef"
          :model="registerForm"
          :rules="registerRules"
          @submit.prevent="handleRegister"
        >
          <el-form-item prop="name">
            <el-input
              v-model="registerForm.name"
              placeholder="请输入您的姓名"
              size="large"
              :prefix-icon="User"
            />
          </el-form-item>
          
          <el-form-item prop="username">
            <el-input
              v-model="registerForm.username"
              placeholder="创建用户名"
              size="large"
              :prefix-icon="Avatar"
            />
          </el-form-item>
          
          <el-form-item prop="password">
            <el-input
              v-model="registerForm.password"
              type="password"
              placeholder="设置密码"
              size="large"
              :prefix-icon="Lock"
              show-password
            />
          </el-form-item>
          
          <el-form-item prop="role">
            <el-select
              v-model="registerForm.role"
              placeholder="选择角色"
              size="large"
              style="width: 100%"
            >
              <el-option label="服务员" value="STAFF" />
              <el-option label="管理员" value="ADMIN" />
            </el-select>
          </el-form-item>
          
          <el-form-item>
            <el-button
              type="primary"
              size="large"
              style="width: 100%; background: #2ecc71; border-color: #2ecc71"
              :loading="loading"
              @click="handleRegister"
            >
              <el-icon><Plus /></el-icon>
              注册
            </el-button>
          </el-form-item>
        </el-form>
        
        <div class="footer">
          已有账户？ 
          <router-link to="/login" class="link">立即登录</router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { User, Lock, Plus, UserFilled, Avatar } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const registerFormRef = ref()
const loading = ref(false)

const registerForm = reactive({
  name: '',
  username: '',
  password: '',
  role: 'STAFF'
})

const registerRules = {
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, message: '用户名长度不能少于3位', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ]
}

const handleRegister = async () => {
  if (!registerFormRef.value) return

  await registerFormRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        console.log('开始注册请求...', registerForm)
        await authStore.register(registerForm)
        ElMessage.success('注册成功！请登录')
        router.push('/login')
      } catch (error) {
        console.error('注册错误:', error)
        if (error.code === 'ERR_NETWORK') {
          ElMessage.error('网络连接失败，请检查后端服务是否启动')
        } else if (error.response) {
          ElMessage.error(error.response.data?.message || '注册失败')
        } else {
          ElMessage.error('网络错误，请检查网络连接')
        }
      } finally {
        loading.value = false
      }
    }
  })
}
</script>

<style scoped>
.register-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.auth-container {
  max-width: 500px;
  width: 100%;
  background: white;
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.auth-header {
  background: #2ecc71;
  color: white;
  text-align: center;
  padding: 30px;
}

.header-icon {
  font-size: 2rem;
  margin-bottom: 10px;
}

.auth-header h2 {
  font-size: 1.8rem;
  margin-bottom: 10px;
}

.auth-body {
  padding: 30px;
}

.footer {
  text-align: center;
  margin-top: 20px;
  color: #6c757d;
  font-size: 0.9rem;
}

.link {
  color: #3498db;
  text-decoration: none;
}

.link:hover {
  text-decoration: underline;
}
</style>
