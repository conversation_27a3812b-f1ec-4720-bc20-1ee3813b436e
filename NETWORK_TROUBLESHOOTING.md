# 网络连接问题排查指南

## 🚨 问题描述
前端登录和注册时报"网络错误，请检查网络连接"

## 🔍 排查步骤

### 1. 检查后端服务状态

**方法一：使用调试脚本**
```powershell
.\debug-connection.ps1
```

**方法二：手动检查**
```powershell
# 检查后端是否启动
Invoke-WebRequest -Uri "http://localhost:8080/test/hello"

# 检查端口占用
Get-NetTCPConnection -LocalPort 8080
```

### 2. 检查前端服务状态

```powershell
# 检查前端是否启动
Invoke-WebRequest -Uri "http://localhost:3000"

# 检查端口占用
Get-NetTCPConnection -LocalPort 3000
```

### 3. 使用连接测试页面

访问: http://localhost:3000/test

这个页面提供了完整的连接测试功能。

### 4. 检查浏览器控制台

1. 打开浏览器开发者工具 (F12)
2. 查看 Console 标签页的错误信息
3. 查看 Network 标签页的请求状态

## 🛠️ 常见问题及解决方案

### 问题1: 后端服务未启动
**症状**: 无法访问 http://localhost:8080/test/hello
**解决方案**:
```powershell
cd backend
mvn spring-boot:run
```
或使用脚本:
```powershell
.\start-backend.ps1
```

### 问题2: 前端服务未启动
**症状**: 无法访问 http://localhost:3000
**解决方案**:
```powershell
cd frontend
npm install
npm run dev
```
或使用脚本:
```powershell
.\start-frontend.ps1
```

### 问题3: 端口被占用
**症状**: 启动时提示端口已被使用
**解决方案**:
```powershell
# 查找占用端口的进程
netstat -ano | findstr :8080
netstat -ano | findstr :3000

# 结束进程 (替换PID为实际进程ID)
taskkill /PID <PID> /F
```

### 问题4: CORS跨域问题
**症状**: 浏览器控制台显示CORS错误
**解决方案**: 已在后端配置CORS，如果仍有问题，检查:
1. 后端CorsConfig.java配置
2. application.yml中的CORS设置

### 问题5: 防火墙阻止
**症状**: 连接超时
**解决方案**:
1. 临时关闭防火墙测试
2. 添加端口例外规则

### 问题6: 数据库连接问题
**症状**: 后端启动失败或500错误
**解决方案**:
1. 确保MySQL服务已启动
2. 检查数据库连接配置
3. 确保数据库已创建并导入数据

## 🔧 配置检查清单

### 后端配置
- [ ] application.yml中的数据库配置正确
- [ ] CORS配置包含前端地址
- [ ] 端口8080未被占用
- [ ] MySQL服务已启动
- [ ] 数据库已创建并导入数据

### 前端配置
- [ ] .env文件中的API地址正确
- [ ] package.json依赖已安装
- [ ] 端口3000未被占用
- [ ] vite.config.js配置正确

## 📝 日志检查

### 后端日志
查看文件: `backend/logs/hotel-order-system.log`

### 前端日志
查看浏览器控制台 (F12 -> Console)

## 🧪 测试命令

### 测试后端API
```powershell
# 基础连接测试
curl http://localhost:8080/test/hello

# 登录API测试
curl -X POST http://localhost:8080/api/auth/login `
  -H "Content-Type: application/json" `
  -d '{"username":"admin","password":"123456"}'
```

### 测试前端
```powershell
# 访问前端首页
curl http://localhost:3000

# 访问测试页面
curl http://localhost:3000/test
```

## 🚀 快速修复

如果以上步骤都无法解决问题，尝试以下快速修复:

1. **重启所有服务**
```powershell
# 停止所有服务 (Ctrl+C)
# 重新启动后端
.\start-backend.ps1
# 重新启动前端 (新窗口)
.\start-frontend.ps1
```

2. **清理并重新安装**
```powershell
# 清理前端依赖
cd frontend
Remove-Item node_modules -Recurse -Force
Remove-Item package-lock.json -Force
npm install

# 清理后端编译
cd backend
mvn clean compile
```

3. **检查系统环境**
- 确保Java 17+已安装
- 确保Node.js 16+已安装
- 确保MySQL 5.7+已安装并运行

## 📞 获取帮助

如果问题仍然存在，请提供以下信息:
1. 操作系统版本
2. Java版本 (`java -version`)
3. Node.js版本 (`node --version`)
4. MySQL版本
5. 浏览器控制台错误信息
6. 后端日志文件内容
7. 调试脚本输出结果
