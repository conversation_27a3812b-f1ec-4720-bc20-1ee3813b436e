<template>
  <Layout>
    <div class="add-dish">
      <div class="page-header">
        <h2>
          <el-icon><Plus /></el-icon>
          添加菜品
        </h2>
        <el-button @click="goBack">
          <el-icon><ArrowLeft /></el-icon>
          返回菜品列表
        </el-button>
      </div>
      
      <el-card shadow="hover">
        <el-form
          ref="dishFormRef"
          :model="dishForm"
          :rules="dishRules"
          label-width="100px"
          style="max-width: 600px; margin: 0 auto;"
        >
          <el-form-item label="菜品名称" prop="name">
            <el-input
              v-model="dishForm.name"
              placeholder="请输入菜品名称"
              size="large"
            />
          </el-form-item>
          
          <el-form-item label="菜品描述" prop="description">
            <el-input
              v-model="dishForm.description"
              type="textarea"
              :rows="4"
              placeholder="请输入菜品描述"
              size="large"
            />
          </el-form-item>
          
          <el-form-item label="价格" prop="price">
            <el-input-number
              v-model="dishForm.price"
              :min="0"
              :precision="2"
              placeholder="请输入价格"
              size="large"
              style="width: 100%"
            >
              <template #prepend>¥</template>
            </el-input-number>
          </el-form-item>
          
          <el-form-item label="菜品分类" prop="category">
            <el-select
              v-model="dishForm.category"
              placeholder="请选择菜品分类"
              size="large"
              style="width: 100%"
            >
              <el-option label="开胃菜" value="APPETIZER">
                <span style="float: left">开胃菜</span>
                <span style="float: right; color: #8492a6; font-size: 13px">APPETIZER</span>
              </el-option>
              <el-option label="主菜" value="MAIN">
                <span style="float: left">主菜</span>
                <span style="float: right; color: #8492a6; font-size: 13px">MAIN</span>
              </el-option>
              <el-option label="甜点" value="DESSERT">
                <span style="float: left">甜点</span>
                <span style="float: right; color: #8492a6; font-size: 13px">DESSERT</span>
              </el-option>
              <el-option label="饮品" value="DRINK">
                <span style="float: left">饮品</span>
                <span style="float: right; color: #8492a6; font-size: 13px">DRINK</span>
              </el-option>
            </el-select>
          </el-form-item>
          
          <el-form-item label="菜品状态" prop="status">
            <el-radio-group v-model="dishForm.status" size="large">
              <el-radio :label="1">
                <el-icon><Check /></el-icon>
                立即上架
              </el-radio>
              <el-radio :label="0">
                <el-icon><Close /></el-icon>
                暂不上架
              </el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item label="菜品图片">
            <el-upload
              class="image-uploader"
              :action="uploadUrl"
              :headers="uploadHeaders"
              :show-file-list="false"
              :on-success="handleImageSuccess"
              :before-upload="beforeImageUpload"
              accept="image/*"
            >
              <img v-if="dishForm.imageUrl" :src="dishForm.imageUrl" class="uploaded-image" />
              <div v-else class="upload-placeholder">
                <el-icon class="upload-icon"><Plus /></el-icon>
                <div class="upload-text">点击上传图片</div>
                <div class="upload-hint">支持 JPG、PNG 格式，大小不超过 2MB</div>
              </div>
            </el-upload>
          </el-form-item>
          
          <el-form-item>
            <div class="form-actions">
              <el-button size="large" @click="goBack">
                <el-icon><Close /></el-icon>
                取消
              </el-button>
              <el-button type="primary" size="large" :loading="submitLoading" @click="submitForm">
                <el-icon><Check /></el-icon>
                保存菜品
              </el-button>
            </div>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
  </Layout>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Plus, ArrowLeft, Check, Close
} from '@element-plus/icons-vue'
import Layout from '@/components/Layout.vue'
import { useAuthStore } from '@/stores/auth'
import api from '@/utils/api'

const router = useRouter()
const authStore = useAuthStore()

const submitLoading = ref(false)
const dishFormRef = ref()

const dishForm = reactive({
  name: '',
  description: '',
  price: 0,
  category: '',
  status: 1,
  imageUrl: ''
})

const dishRules = {
  name: [
    { required: true, message: '请输入菜品名称', trigger: 'blur' },
    { min: 2, max: 50, message: '菜品名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入菜品描述', trigger: 'blur' },
    { min: 5, max: 200, message: '菜品描述长度在 5 到 200 个字符', trigger: 'blur' }
  ],
  price: [
    { required: true, message: '请输入价格', trigger: 'blur' },
    { type: 'number', min: 0, message: '价格不能小于0', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择菜品分类', trigger: 'change' }
  ]
}

const uploadUrl = '/api/dishes/upload'
const uploadHeaders = {
  Authorization: `Bearer ${authStore.token}`
}

const goBack = () => {
  router.push('/dishes')
}

const submitForm = async () => {
  if (!dishFormRef.value) return
  
  await dishFormRef.value.validate(async (valid) => {
    if (valid) {
      submitLoading.value = true
      try {
        await api.post('/api/dishes', dishForm)
        ElMessage.success('菜品添加成功')
        router.push('/dishes')
      } catch (error) {
        console.error('添加菜品失败:', error)
        ElMessage.error(error.response?.data?.message || '添加菜品失败')
      } finally {
        submitLoading.value = false
      }
    }
  })
}

const handleImageSuccess = (response) => {
  if (response.code === 200) {
    dishForm.imageUrl = response.data.url
    ElMessage.success('图片上传成功')
  } else {
    ElMessage.error('图片上传失败')
  }
}

const beforeImageUpload = (file) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJPG) {
    ElMessage.error('上传图片只能是 JPG/PNG 格式!')
  }
  if (!isLt2M) {
    ElMessage.error('上传图片大小不能超过 2MB!')
  }
  return isJPG && isLt2M
}
</script>

<style scoped>
.add-dish {
  max-width: 1000px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.page-header h2 {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #3498db;
  margin: 0;
  font-size: 1.5rem;
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 30px;
}

.image-uploader {
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.3s;
  width: 200px;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-uploader:hover {
  border-color: #409eff;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.upload-placeholder {
  text-align: center;
  color: #8c939d;
}

.upload-icon {
  font-size: 28px;
  margin-bottom: 10px;
}

.upload-text {
  font-size: 14px;
  margin-bottom: 5px;
}

.upload-hint {
  font-size: 12px;
  color: #999;
}

:deep(.el-form-item__label) {
  font-weight: 600;
  color: #333;
}

:deep(.el-input__inner) {
  border-radius: 6px;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-radio) {
  margin-right: 30px;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .form-actions {
    flex-direction: column;
  }
}
</style>
