<template>
  <div class="connection-test">
    <el-card class="test-card">
      <template #header>
        <h2>连接测试</h2>
      </template>
      
      <div class="test-section">
        <h3>后端连接测试</h3>
        <el-button @click="testBackendConnection" :loading="testing">
          测试后端连接
        </el-button>
        <div v-if="testResult" class="test-result">
          <el-alert
            :title="testResult.title"
            :type="testResult.type"
            :description="testResult.message"
            show-icon
          />
        </div>
      </div>
      
      <div class="test-section">
        <h3>API端点测试</h3>
        <el-button @click="testApiEndpoints" :loading="testingApi">
          测试API端点
        </el-button>
        <div v-if="apiResults.length > 0" class="api-results">
          <div v-for="result in apiResults" :key="result.url" class="api-result">
            <el-tag :type="result.success ? 'success' : 'danger'">
              {{ result.url }} - {{ result.status }}
            </el-tag>
          </div>
        </div>
      </div>
      
      <div class="test-section">
        <h3>登录测试</h3>
        <el-form :model="loginForm" inline>
          <el-form-item label="用户名">
            <el-input v-model="loginForm.username" placeholder="admin" />
          </el-form-item>
          <el-form-item label="密码">
            <el-input v-model="loginForm.password" type="password" placeholder="123456" />
          </el-form-item>
          <el-form-item>
            <el-button @click="testLogin" :loading="testingLogin">
              测试登录
            </el-button>
          </el-form-item>
        </el-form>
        <div v-if="loginResult" class="test-result">
          <el-alert
            :title="loginResult.title"
            :type="loginResult.type"
            :description="loginResult.message"
            show-icon
          />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import axios from 'axios'

const testing = ref(false)
const testingApi = ref(false)
const testingLogin = ref(false)
const testResult = ref(null)
const loginResult = ref(null)
const apiResults = ref([])

const loginForm = reactive({
  username: 'admin',
  password: '123456'
})

const testBackendConnection = async () => {
  testing.value = true
  testResult.value = null
  
  try {
    const response = await axios.get('http://localhost:8080/test/hello', {
      timeout: 5000
    })
    
    testResult.value = {
      title: '连接成功',
      type: 'success',
      message: `后端服务正常运行。响应: ${response.data}`
    }
  } catch (error) {
    console.error('连接测试错误:', error)
    testResult.value = {
      title: '连接失败',
      type: 'error',
      message: `错误: ${error.message}. 请检查后端服务是否启动在 http://localhost:8080`
    }
  } finally {
    testing.value = false
  }
}

const testApiEndpoints = async () => {
  testingApi.value = true
  apiResults.value = []
  
  const endpoints = [
    'http://localhost:8080/test/hello',
    'http://localhost:8080/test/health',
    'http://localhost:8080/test/info'
  ]
  
  for (const endpoint of endpoints) {
    try {
      const response = await axios.get(endpoint, { timeout: 5000 })
      apiResults.value.push({
        url: endpoint,
        success: true,
        status: `成功 (${response.status})`
      })
    } catch (error) {
      apiResults.value.push({
        url: endpoint,
        success: false,
        status: `失败 (${error.message})`
      })
    }
  }
  
  testingApi.value = false
}

const testLogin = async () => {
  testingLogin.value = true
  loginResult.value = null
  
  try {
    const response = await axios.post('http://localhost:8080/api/auth/login', loginForm, {
      timeout: 5000,
      headers: {
        'Content-Type': 'application/json'
      }
    })
    
    loginResult.value = {
      title: '登录成功',
      type: 'success',
      message: `登录成功！Token: ${response.data.data?.token?.substring(0, 20)}...`
    }
  } catch (error) {
    console.error('登录测试错误:', error)
    loginResult.value = {
      title: '登录失败',
      type: 'error',
      message: `错误: ${error.response?.data?.message || error.message}`
    }
  } finally {
    testingLogin.value = false
  }
}
</script>

<style scoped>
.connection-test {
  max-width: 800px;
  margin: 50px auto;
  padding: 20px;
}

.test-card {
  margin-bottom: 20px;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
}

.test-section h3 {
  margin-bottom: 15px;
  color: #3498db;
}

.test-result {
  margin-top: 15px;
}

.api-results {
  margin-top: 15px;
}

.api-result {
  margin-bottom: 10px;
}
</style>
