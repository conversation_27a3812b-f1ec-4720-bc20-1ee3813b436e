<template>
  <Layout>
    <div class="users">
      <!-- 页面标题 -->
      <div class="page-header">
        <h2>
          <el-icon><User /></el-icon>
          用户管理
        </h2>
        <el-button type="primary" @click="showAddDialog">
          <el-icon><Plus /></el-icon>
          添加用户
        </el-button>
      </div>
      
      <!-- 搜索筛选 -->
      <el-card class="filter-card" shadow="never">
        <el-form :model="searchForm" label-width="80px">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="用户名">
                <el-input
                  v-model="searchForm.username"
                  placeholder="请输入用户名"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="姓名">
                <el-input
                  v-model="searchForm.name"
                  placeholder="请输入姓名"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="角色">
                <el-select v-model="searchForm.role" placeholder="请选择角色" clearable style="width: 100%">
                  <el-option label="管理员" value="ADMIN" />
                  <el-option label="服务员" value="STAFF" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label=" ">
                <el-button type="primary" @click="handleSearch" size="default">
                  <el-icon><Search /></el-icon>
                  搜索
                </el-button>
                <el-button @click="resetSearch" size="default" style="margin-left: 10px;">
                  <el-icon><Refresh /></el-icon>
                  重置
                </el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>
      
      <!-- 用户列表 -->
      <el-card shadow="hover">
        <el-table :data="users" v-loading="loading" stripe>
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="username" label="用户名" width="120" />
          <el-table-column prop="name" label="姓名" width="120" />
          <el-table-column prop="role" label="角色" width="100">
            <template #default="{ row }">
              <el-tag :type="row.role === 'ADMIN' ? 'danger' : 'success'">
                {{ row.role === 'ADMIN' ? '管理员' : '服务员' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="row.status === 1 ? 'success' : 'danger'">
                {{ row.status === 1 ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="lastLoginAt" label="最后登录" width="180" />
          <el-table-column prop="createdAt" label="创建时间" width="180" />
          <el-table-column label="操作" width="260">
            <template #default="{ row }">
              <div class="action-buttons">
                <el-button type="primary" size="small" @click="editUser(row)">
                  <el-icon><Edit /></el-icon>
                  编辑
                </el-button>
                <el-button
                  :type="row.status === 1 ? 'warning' : 'success'"
                  size="small"
                  @click="toggleUserStatus(row)"
                >
                  <el-icon v-if="row.status === 1"><Lock /></el-icon>
                  <el-icon v-else><Unlock /></el-icon>
                  {{ row.status === 1 ? '禁用' : '启用' }}
                </el-button>
                <el-button
                  type="danger"
                  size="small"
                  @click="deleteUser(row)"
                >
                  <el-icon><Delete /></el-icon>
                  删除
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.size"
            :page-sizes="[10, 20, 50]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>
    
    <!-- 添加/编辑用户对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑用户' : '添加用户'"
      width="500px"
      @close="resetForm"
    >
      <el-form
        ref="userFormRef"
        :model="userForm"
        :rules="userRules"
        label-width="80px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input
            v-model="userForm.username"
            placeholder="请输入用户名"
            :disabled="isEdit"
          />
        </el-form-item>
        
        <el-form-item label="姓名" prop="name">
          <el-input v-model="userForm.name" placeholder="请输入姓名" />
        </el-form-item>
        
        <el-form-item v-if="!isEdit" label="密码" prop="password">
          <el-input
            v-model="userForm.password"
            type="password"
            placeholder="请输入密码"
            show-password
          />
        </el-form-item>
        
        <el-form-item label="角色" prop="role">
          <el-select v-model="userForm.role" placeholder="请选择角色" style="width: 100%">
            <el-option label="管理员" value="ADMIN" />
            <el-option label="服务员" value="STAFF" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="userForm.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="submitForm">
          {{ isEdit ? '更新' : '添加' }}
        </el-button>
      </template>
    </el-dialog>
  </Layout>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  User, Plus, Search, Refresh, Edit, Lock, Unlock, Delete
} from '@element-plus/icons-vue'
import Layout from '@/components/Layout.vue'
import api from '@/utils/api'

const loading = ref(false)
const dialogVisible = ref(false)
const isEdit = ref(false)
const submitLoading = ref(false)
const userFormRef = ref()

const users = ref([])

const searchForm = reactive({
  username: '',
  name: '',
  role: ''
})

const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

const userForm = reactive({
  id: null,
  username: '',
  name: '',
  password: '',
  role: 'STAFF',
  status: 1
})

const userRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, message: '用户名长度不能少于3位', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ]
}

const loadUsers = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      ...searchForm
    }

    const response = await api.get('/api/users', { params })
    const { records, total } = response.data.data

    users.value = records
    pagination.total = total
  } catch (error) {
    console.error('加载用户列表失败:', error)
    // 使用模拟数据
    users.value = [
      {
        id: 1,
        username: 'admin',
        name: '系统管理员',
        role: 'ADMIN',
        status: 1,
        lastLoginAt: '2024-01-15 12:30:00',
        createdAt: '2024-01-01 09:00:00'
      },
      {
        id: 2,
        username: 'staff1',
        name: '张小明',
        role: 'STAFF',
        status: 1,
        lastLoginAt: '2024-01-15 11:45:00',
        createdAt: '2024-01-02 10:30:00'
      },
      {
        id: 3,
        username: 'staff2',
        name: '李小红',
        role: 'STAFF',
        status: 1,
        lastLoginAt: '2024-01-15 10:20:00',
        createdAt: '2024-01-03 14:15:00'
      },
      {
        id: 4,
        username: 'staff3',
        name: '王小华',
        role: 'STAFF',
        status: 0,
        lastLoginAt: '2024-01-10 16:30:00',
        createdAt: '2024-01-05 11:00:00'
      },
      {
        id: 5,
        username: 'manager1',
        name: '刘经理',
        role: 'ADMIN',
        status: 1,
        lastLoginAt: '2024-01-14 18:00:00',
        createdAt: '2024-01-01 15:30:00'
      },
      {
        id: 6,
        username: 'staff4',
        name: '陈小军',
        role: 'STAFF',
        status: 1,
        lastLoginAt: '2024-01-15 08:30:00',
        createdAt: '2024-01-08 09:45:00'
      }
    ]
    pagination.total = users.value.length
    ElMessage.warning('使用模拟用户数据')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadUsers()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    username: '',
    name: '',
    role: ''
  })
  handleSearch()
}

const handleSizeChange = (size) => {
  pagination.size = size
  loadUsers()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  loadUsers()
}

const showAddDialog = () => {
  isEdit.value = false
  dialogVisible.value = true
}

const editUser = (user) => {
  isEdit.value = true
  Object.assign(userForm, {
    id: user.id,
    username: user.username,
    name: user.name,
    role: user.role,
    status: user.status,
    password: ''
  })
  dialogVisible.value = true
}

const toggleUserStatus = async (user) => {
  const newStatus = user.status === 1 ? 0 : 1
  const action = newStatus === 1 ? '启用' : '禁用'
  
  try {
    await ElMessageBox.confirm(
      `确定要${action}用户"${user.name}"吗？`,
      `确认${action}`,
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await api.put(`/api/users/${user.id}`, { ...user, status: newStatus })
    ElMessage.success(`${action}成功`)
    loadUsers()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(`${action}失败`)
    }
  }
}

const deleteUser = async (user) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户"${user.name}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await api.delete(`/api/users/${user.id}`)
    ElMessage.success('删除成功')
    loadUsers()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const resetForm = () => {
  Object.assign(userForm, {
    id: null,
    username: '',
    name: '',
    password: '',
    role: 'STAFF',
    status: 1
  })
  userFormRef.value?.resetFields()
}

const submitForm = async () => {
  if (!userFormRef.value) return
  
  await userFormRef.value.validate(async (valid) => {
    if (valid) {
      submitLoading.value = true
      try {
        if (isEdit.value) {
          const updateData = { ...userForm }
          if (!updateData.password) {
            delete updateData.password
          }
          await api.put(`/api/users/${userForm.id}`, updateData)
          ElMessage.success('更新成功')
        } else {
          await api.post('/api/users', userForm)
          ElMessage.success('添加成功')
        }
        
        dialogVisible.value = false
        loadUsers()
      } catch (error) {
        ElMessage.error(isEdit.value ? '更新失败' : '添加失败')
      } finally {
        submitLoading.value = false
      }
    }
  })
}

onMounted(() => {
  loadUsers()
})
</script>

<style scoped>
.users {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #3498db;
  margin: 0;
}

.filter-card {
  margin-bottom: 20px;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.action-buttons .el-button {
  margin: 0;
  padding: 5px 8px;
  font-size: 12px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
}
</style>
