<template>
  <Layout>
    <div class="create-order">
      <div class="page-header">
        <h2>
          <el-icon><DocumentAdd /></el-icon>
          创建订单
        </h2>
        <el-button @click="goBack">
          <el-icon><ArrowLeft /></el-icon>
          返回订单列表
        </el-button>
      </div>
      
      <el-row :gutter="20">
        <!-- 左侧：订单信息 -->
        <el-col :span="8">
          <el-card shadow="hover" class="order-info-card">
            <template #header>
              <h3>
                <el-icon><Edit /></el-icon>
                订单信息
              </h3>
            </template>
            
            <el-form
              ref="orderFormRef"
              :model="orderForm"
              :rules="orderRules"
              label-width="80px"
            >
              <el-form-item label="桌号" prop="tableNumber">
                <el-input
                  v-model="orderForm.tableNumber"
                  placeholder="请输入桌号"
                  size="large"
                >
                  <template #prepend>桌</template>
                </el-input>
              </el-form-item>
              
              <el-form-item label="备注">
                <el-input
                  v-model="orderForm.notes"
                  type="textarea"
                  :rows="3"
                  placeholder="订单备注（可选）"
                />
              </el-form-item>
            </el-form>
            
            <!-- 已选菜品 -->
            <div class="selected-dishes">
              <h4>
                <el-icon><List /></el-icon>
                已选菜品 ({{ orderForm.items.length }})
              </h4>
              
              <div v-if="orderForm.items.length === 0" class="empty-cart">
                <el-icon><ShoppingCart /></el-icon>
                <p>还没有选择菜品</p>
              </div>
              
              <div v-else class="cart-items">
                <div
                  v-for="(item, index) in orderForm.items"
                  :key="item.dishId"
                  class="cart-item"
                >
                  <div class="item-info">
                    <h5>{{ item.dishName }}</h5>
                    <p class="item-price">¥{{ item.price }}</p>
                  </div>
                  <div class="item-controls">
                    <el-input-number
                      v-model="item.quantity"
                      :min="1"
                      size="small"
                      @change="updateTotal"
                    />
                    <el-button
                      type="danger"
                      size="small"
                      @click="removeItem(index)"
                    >
                      <el-icon><Delete /></el-icon>
                    </el-button>
                  </div>
                </div>
              </div>
              
              <!-- 总计 -->
              <div class="order-total">
                <div class="total-line">
                  <span>总计：</span>
                  <span class="total-amount">¥{{ orderForm.totalPrice.toFixed(2) }}</span>
                </div>
              </div>
              
              <!-- 提交按钮 -->
              <div class="submit-actions">
                <el-button size="large" @click="goBack">
                  <el-icon><Close /></el-icon>
                  取消
                </el-button>
                <el-button
                  type="primary"
                  size="large"
                  :loading="submitLoading"
                  :disabled="orderForm.items.length === 0"
                  @click="submitOrder"
                >
                  <el-icon><Check /></el-icon>
                  创建订单
                </el-button>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <!-- 右侧：菜品选择 -->
        <el-col :span="16">
          <el-card shadow="hover" class="dishes-card">
            <template #header>
              <div class="dishes-header">
                <h3>
                  <el-icon><Food /></el-icon>
                  选择菜品
                </h3>
                <el-input
                  v-model="searchKeyword"
                  placeholder="搜索菜品..."
                  style="width: 200px"
                  clearable
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
              </div>
            </template>
            
            <!-- 分类筛选 -->
            <div class="category-filter">
              <el-button
                :type="selectedCategory === '' ? 'primary' : ''"
                @click="selectedCategory = ''"
              >
                全部
              </el-button>
              <el-button
                :type="selectedCategory === 'APPETIZER' ? 'primary' : ''"
                @click="selectedCategory = 'APPETIZER'"
              >
                开胃菜
              </el-button>
              <el-button
                :type="selectedCategory === 'MAIN' ? 'primary' : ''"
                @click="selectedCategory = 'MAIN'"
              >
                主菜
              </el-button>
              <el-button
                :type="selectedCategory === 'DESSERT' ? 'primary' : ''"
                @click="selectedCategory = 'DESSERT'"
              >
                甜点
              </el-button>
              <el-button
                :type="selectedCategory === 'DRINK' ? 'primary' : ''"
                @click="selectedCategory = 'DRINK'"
              >
                饮品
              </el-button>
            </div>
            
            <!-- 菜品网格 -->
            <div class="dishes-grid" v-loading="dishesLoading">
              <div
                v-for="dish in filteredDishes"
                :key="dish.id"
                class="dish-item"
                @click="addDishToOrder(dish)"
              >
                <div class="dish-image">
                  <img :src="dish.imageUrl || '/default-dish.svg'" :alt="dish.name" />
                  <div class="dish-overlay">
                    <el-icon><Plus /></el-icon>
                  </div>
                </div>
                <div class="dish-details">
                  <h4>{{ dish.name }}</h4>
                  <p class="dish-desc">{{ dish.description }}</p>
                  <div class="dish-footer">
                    <span class="dish-price">¥{{ dish.price }}</span>
                    <el-tag :type="getCategoryType(dish.category)" size="small">
                      {{ getCategoryText(dish.category) }}
                    </el-tag>
                  </div>
                </div>
              </div>
            </div>
            
            <div v-if="filteredDishes.length === 0 && !dishesLoading" class="no-dishes">
              <el-icon><Warning /></el-icon>
              <p>没有找到符合条件的菜品</p>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </Layout>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  DocumentAdd, ArrowLeft, Edit, List, ShoppingCart, Delete, Close, Check,
  Food, Search, Plus, Warning
} from '@element-plus/icons-vue'
import Layout from '@/components/Layout.vue'
import api from '@/utils/api'

const router = useRouter()

const submitLoading = ref(false)
const dishesLoading = ref(false)
const orderFormRef = ref()
const searchKeyword = ref('')
const selectedCategory = ref('')

const availableDishes = ref([])

const orderForm = reactive({
  tableNumber: '',
  notes: '',
  items: [],
  totalPrice: 0
})

const orderRules = {
  tableNumber: [
    { required: true, message: '请输入桌号', trigger: 'blur' }
  ]
}

const filteredDishes = computed(() => {
  let dishes = availableDishes.value
  
  // 按分类筛选
  if (selectedCategory.value) {
    dishes = dishes.filter(dish => dish.category === selectedCategory.value)
  }
  
  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    dishes = dishes.filter(dish => 
      dish.name.toLowerCase().includes(keyword) ||
      dish.description.toLowerCase().includes(keyword)
    )
  }
  
  return dishes
})

const loadAvailableDishes = async () => {
  dishesLoading.value = true
  try {
    const response = await api.get('/api/dishes/available')
    availableDishes.value = response.data.data
  } catch (error) {
    console.error('加载菜品失败:', error)
    // 使用模拟数据
    availableDishes.value = [
      {
        id: 1,
        name: '凉拌黄瓜',
        description: '清爽开胃，夏日必备的凉菜',
        price: 18,
        category: 'APPETIZER',
        status: 1,
        imageUrl: '/default-dish.svg'
      },
      {
        id: 2,
        name: '宫保鸡丁',
        description: '经典川菜，香辣可口，下饭神器',
        price: 38,
        category: 'MAIN',
        status: 1,
        imageUrl: '/default-dish.svg'
      },
      {
        id: 3,
        name: '蛋炒饭',
        description: '简单美味，老少皆宜的经典主食',
        price: 25,
        category: 'MAIN',
        status: 1,
        imageUrl: '/default-dish.svg'
      },
      {
        id: 4,
        name: '清蒸鲈鱼',
        description: '鲜美嫩滑，营养丰富的健康菜品',
        price: 68.1,
        category: 'MAIN',
        status: 1,
        imageUrl: '/default-dish.svg'
      },
      {
        id: 5,
        name: '可乐',
        description: '冰爽可乐，解腻必备饮品',
        price: 10,
        category: 'DRINK',
        status: 1,
        imageUrl: '/default-dish.svg'
      },
      {
        id: 6,
        name: '红烧肉',
        description: '肥而不腻，入口即化的经典菜品',
        price: 45,
        category: 'MAIN',
        status: 1,
        imageUrl: '/default-dish.svg'
      },
      {
        id: 8,
        name: '酸辣土豆丝',
        description: '酸辣开胃，爽脆可口',
        price: 15,
        category: 'APPETIZER',
        status: 1,
        imageUrl: '/default-dish.svg'
      },
      {
        id: 9,
        name: '橙汁',
        description: '新鲜橙汁，维C丰富',
        price: 12,
        category: 'DRINK',
        status: 1,
        imageUrl: '/default-dish.svg'
      }
    ]
    ElMessage.warning('使用模拟菜品数据')
  } finally {
    dishesLoading.value = false
  }
}

const addDishToOrder = (dish) => {
  const existingItem = orderForm.items.find(item => item.dishId === dish.id)
  
  if (existingItem) {
    existingItem.quantity += 1
    ElMessage.success(`${dish.name} 数量 +1`)
  } else {
    orderForm.items.push({
      dishId: dish.id,
      dishName: dish.name,
      price: dish.price,
      quantity: 1,
      notes: ''
    })
    ElMessage.success(`已添加 ${dish.name}`)
  }
  
  updateTotal()
}

const removeItem = (index) => {
  const item = orderForm.items[index]
  orderForm.items.splice(index, 1)
  updateTotal()
  ElMessage.info(`已移除 ${item.dishName}`)
}

const updateTotal = () => {
  orderForm.totalPrice = orderForm.items.reduce(
    (total, item) => total + (item.price * item.quantity),
    0
  )
}

const goBack = () => {
  router.push('/orders')
}

const submitOrder = async () => {
  if (!orderFormRef.value) return
  
  await orderFormRef.value.validate(async (valid) => {
    if (valid) {
      if (orderForm.items.length === 0) {
        ElMessage.warning('请至少选择一个菜品')
        return
      }
      
      submitLoading.value = true
      try {
        await api.post('/api/orders', orderForm)
        ElMessage.success('订单创建成功')
        router.push('/orders')
      } catch (error) {
        console.error('创建订单失败:', error)
        ElMessage.error(error.response?.data?.message || '创建订单失败')
      } finally {
        submitLoading.value = false
      }
    }
  })
}

const getCategoryType = (category) => {
  const typeMap = {
    'APPETIZER': 'danger',
    'MAIN': 'success',
    'DESSERT': 'warning',
    'DRINK': 'info'
  }
  return typeMap[category] || 'info'
}

const getCategoryText = (category) => {
  const textMap = {
    'APPETIZER': '开胃菜',
    'MAIN': '主菜',
    'DESSERT': '甜点',
    'DRINK': '饮品'
  }
  return textMap[category] || category
}

onMounted(() => {
  loadAvailableDishes()
})
</script>

<style scoped>
.create-order {
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.page-header h2 {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #3498db;
  margin: 0;
  font-size: 1.5rem;
}

.order-info-card {
  height: fit-content;
  position: sticky;
  top: 20px;
}

.dishes-card {
  min-height: 600px;
}

.dishes-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dishes-header h3 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
}

.category-filter {
  margin-bottom: 20px;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.selected-dishes h4 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 20px 0 15px 0;
  color: #3498db;
}

.empty-cart {
  text-align: center;
  padding: 40px 20px;
  color: #999;
}

.empty-cart .el-icon {
  font-size: 48px;
  margin-bottom: 10px;
}

.cart-items {
  max-height: 300px;
  overflow-y: auto;
}

.cart-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  margin-bottom: 10px;
  background: #f8f9fa;
}

.item-info h5 {
  margin: 0 0 5px 0;
  color: #333;
}

.item-price {
  margin: 0;
  color: #3498db;
  font-weight: bold;
}

.item-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.order-total {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 2px solid #e9ecef;
}

.total-line {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 1.2rem;
  font-weight: bold;
}

.total-amount {
  color: #3498db;
  font-size: 1.4rem;
}

.submit-actions {
  margin-top: 20px;
  display: flex;
  gap: 10px;
}

.dishes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
  margin-top: 20px;
}

.dish-item {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s;
  background: white;
}

.dish-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #3498db;
}

.dish-image {
  position: relative;
  height: 120px;
  overflow: hidden;
}

.dish-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.dish-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(52, 152, 219, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
}

.dish-item:hover .dish-overlay {
  opacity: 1;
}

.dish-overlay .el-icon {
  font-size: 24px;
  color: white;
}

.dish-details {
  padding: 12px;
}

.dish-details h4 {
  margin: 0 0 5px 0;
  color: #333;
  font-size: 0.9rem;
}

.dish-desc {
  margin: 0 0 10px 0;
  color: #666;
  font-size: 0.8rem;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.dish-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dish-price {
  color: #3498db;
  font-weight: bold;
  font-size: 1rem;
}

.no-dishes {
  text-align: center;
  padding: 60px 20px;
  color: #999;
}

.no-dishes .el-icon {
  font-size: 48px;
  margin-bottom: 10px;
}

@media (max-width: 1200px) {
  .dishes-grid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .dishes-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .category-filter {
    justify-content: center;
  }

  .dishes-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }

  .submit-actions {
    flex-direction: column;
  }
}
</style>
