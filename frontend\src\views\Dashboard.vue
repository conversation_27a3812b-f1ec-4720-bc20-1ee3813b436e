<template>
  <Layout>
    <div class="dashboard">
      <!-- 页面标题 -->
      <div class="page-header">
        <h2>
          <el-icon><Odometer /></el-icon>
          控制面板
        </h2>
        <el-button type="primary" @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
      
      <!-- 统计卡片 -->
      <div class="stats-container">
        <el-card class="stat-card" shadow="hover">
          <div class="stat-content">
            <div class="stat-icon today-orders">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stat-info">
              <h3>今日订单</h3>
              <div class="stat-value">{{ stats.todayOrders }}</div>
            </div>
          </div>
        </el-card>
        
        <el-card class="stat-card" shadow="hover">
          <div class="stat-content">
            <div class="stat-icon pending-orders">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stat-info">
              <h3>待处理订单</h3>
              <div class="stat-value">{{ stats.pendingOrders }}</div>
            </div>
          </div>
        </el-card>
        
        <el-card class="stat-card" shadow="hover">
          <div class="stat-content">
            <div class="stat-icon today-sales">
              <el-icon><Money /></el-icon>
            </div>
            <div class="stat-info">
              <h3>今日销售额</h3>
              <div class="stat-value">¥{{ stats.todaySales }}</div>
            </div>
          </div>
        </el-card>
        
        <el-card class="stat-card" shadow="hover">
          <div class="stat-content">
            <div class="stat-icon total-dishes">
              <el-icon><Food /></el-icon>
            </div>
            <div class="stat-info">
              <h3>菜品总数</h3>
              <div class="stat-value">{{ stats.totalDishes }}</div>
            </div>
          </div>
        </el-card>
      </div>
      
      <!-- 热销菜品 -->
      <el-card class="content-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <h3>
              <el-icon><TrendCharts /></el-icon>
              热销菜品
            </h3>
          </div>
        </template>
        
        <div class="dishes-grid" v-loading="dishesLoading">
          <div
            v-for="dish in hotDishes"
            :key="dish.id"
            class="dish-card"
          >
            <div class="dish-image">
              <img :src="dish.imageUrl || '/default-dish.svg'" :alt="dish.name" />
            </div>
            <div class="dish-info">
              <h4>{{ dish.name }}</h4>
              <p>{{ dish.description }}</p>
              <div class="dish-meta">
                <span class="price">¥{{ dish.price }}</span>
                <span class="sales">销量: {{ dish.salesCount }}份</span>
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </Layout>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Odometer, Refresh, Document, Clock, Money, Food, TrendCharts, List
} from '@element-plus/icons-vue'
import Layout from '@/components/Layout.vue'
import api from '@/utils/api'

const router = useRouter()

const stats = reactive({
  todayOrders: 0,
  pendingOrders: 0,
  todaySales: 0,
  totalDishes: 0
})

const hotDishes = ref([])
const recentOrders = ref([])
const dishesLoading = ref(false)
const ordersLoading = ref(false)

const loadStats = async () => {
  try {
    const response = await api.get('/api/orders/today-stats')
    Object.assign(stats, response.data.data)
  } catch (error) {
    console.error('加载统计数据失败:', error)
    // 使用模拟数据
    Object.assign(stats, {
      todayOrders: 15,
      pendingOrders: 3,
      todaySales: 1280.50,
      totalDishes: 28
    })
    ElMessage.warning('使用模拟统计数据')
  }
}

const loadHotDishes = async () => {
  dishesLoading.value = true
  try {
    const response = await api.get('/api/dishes/hot?limit=6')
    hotDishes.value = response.data.data
  } catch (error) {
    console.error('加载热销菜品失败:', error)
    // 使用模拟数据
    hotDishes.value = [
      {
        id: 1,
        name: '凉拌黄瓜',
        description: '清爽开胃，夏日必备',
        price: 18,
        salesCount: 45,
        imageUrl: '/default-dish.svg'
      },
      {
        id: 2,
        name: '宫保鸡丁',
        description: '经典川菜，香辣可口',
        price: 38,
        salesCount: 32,
        imageUrl: '/default-dish.svg'
      },
      {
        id: 3,
        name: '蛋炒饭',
        description: '简单美味，老少皆宜',
        price: 25,
        salesCount: 28,
        imageUrl: '/default-dish.svg'
      },
      {
        id: 4,
        name: '清蒸鲈鱼',
        description: '鲜美嫩滑，营养丰富',
        price: 68.1,
        salesCount: 18,
        imageUrl: '/default-dish.svg'
      },
      {
        id: 5,
        name: '可乐',
        description: '冰爽可乐，解腻必备',
        price: 10,
        salesCount: 56,
        imageUrl: '/default-dish.svg'
      },
      {
        id: 6,
        name: '红烧肉',
        description: '肥而不腻，入口即化',
        price: 45,
        salesCount: 22,
        imageUrl: '/default-dish.svg'
      }
    ]
    ElMessage.warning('使用模拟热销菜品数据')
  } finally {
    dishesLoading.value = false
  }
}

const loadRecentOrders = async () => {
  ordersLoading.value = true
  try {
    const response = await api.get('/api/orders?page=1&size=5')
    recentOrders.value = response.data.data.records
  } catch (error) {
    ElMessage.error('加载最近订单失败')
  } finally {
    ordersLoading.value = false
  }
}

const refreshData = async () => {
  await Promise.all([
    loadStats(),
    loadHotDishes(),
    loadRecentOrders()
  ])
  ElMessage.success('数据已刷新')
}

const getStatusType = (status) => {
  const statusMap = {
    'PENDING': 'warning',
    'PREPARING': 'info',
    'COMPLETED': 'success',
    'CANCELLED': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    'PENDING': '待处理',
    'PREPARING': '准备中',
    'COMPLETED': '已完成',
    'CANCELLED': '已取消'
  }
  return statusMap[status] || status
}

const viewOrder = (orderId) => {
  router.push(`/orders/${orderId}`)
}

onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.dashboard {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #3498db;
  margin: 0;
}

.stats-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  border-left: 4px solid #3498db;
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
}

.today-orders {
  background: #3498db;
}

.pending-orders {
  background: #f39c12;
}

.today-sales {
  background: #2ecc71;
}

.total-dishes {
  background: #e74c3c;
}

.stat-info h3 {
  margin: 0 0 5px 0;
  color: #6c757d;
  font-size: 0.9rem;
  font-weight: normal;
}

.stat-value {
  font-size: 1.8rem;
  font-weight: bold;
  color: #3498db;
}

.content-card {
  margin-bottom: 30px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  color: #3498db;
}

.dishes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.dish-card {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.3s, box-shadow 0.3s;
}

.dish-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.dish-image {
  height: 150px;
  overflow: hidden;
}

.dish-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.dish-info {
  padding: 15px;
}

.dish-info h4 {
  margin: 0 0 8px 0;
  color: #343a40;
}

.dish-info p {
  margin: 0 0 10px 0;
  color: #6c757d;
  font-size: 0.9rem;
  line-height: 1.4;
}

.dish-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.price {
  font-size: 1.2rem;
  font-weight: bold;
  color: #3498db;
}

.sales {
  font-size: 0.9rem;
  color: #6c757d;
}

@media (max-width: 768px) {
  .stats-container {
    grid-template-columns: 1fr;
  }

  .dishes-grid {
    grid-template-columns: 1fr;
  }
}
</style>
